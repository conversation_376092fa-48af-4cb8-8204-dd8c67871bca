/* Content-Type: text/css */
@import url('base.css');

body {
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
  color: white;
  font-family: var(--font-family);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  overflow: hidden;
}

.app-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 800px;
  padding: var(--spacing-4);
  animation: fadeIn 0.5s ease-in-out;
}

.app-header {
  text-align: center;
  margin-bottom: var(--spacing-6);
  width: 100%;
}

.app-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-2);
  background: linear-gradient(90deg, #4361ee, #3a0ca3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-subtitle {
  font-size: var(--font-size-lg);
  color: var(--gray-400);
  margin-bottom: var(--spacing-4);
}

.orb {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: url('/static/image.png') no-repeat center/cover;
  box-shadow: 0 0 30px rgba(67, 97, 238, 0.3);
  margin-bottom: var(--spacing-6);
  animation: pulseOrb 2s infinite ease-in-out;
  transition: var(--transition);
}

.orb.talking {
  animation: pulseOrbFast 1s infinite ease-in-out;
  box-shadow: 0 0 40px rgba(58, 12, 163, 0.5);
}

#controls {
  margin: var(--spacing-5) 0;
  display: flex;
  gap: var(--spacing-3);
}

button {
  padding: var(--spacing-3) var(--spacing-6);
  margin: 0 var(--spacing-2);
  font-size: var(--font-size-base);
  font-weight: 500;
  border: none;
  border-radius: var(--border-radius);
  background: var(--primary-color);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
  justify-content: center;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-md);
  background: var(--secondary-color);
}

button:disabled {
  background: var(--gray-600);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

#chatLog {
  width: 100%;
  max-width: 700px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-5);
  overflow-y: auto;
  max-height: 400px;
  box-shadow: var(--box-shadow-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

#chatLog p {
  margin: var(--spacing-3) 0;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--border-radius);
  animation: fadeIn 0.3s ease-in-out;
  position: relative;
  line-height: 1.5;
}

#chatLog p.agent {
  background: rgba(67, 97, 238, 0.15);
  border-left: 3px solid var(--primary-color);
  margin-right: var(--spacing-8);
}

#chatLog p.user {
  background: rgba(58, 12, 163, 0.15);
  border-right: 3px solid var(--secondary-color);
  margin-left: var(--spacing-8);
  text-align: right;
}

#chatLog p.user.loading {
  background: rgba(58, 12, 163, 0.05);
  border-right: 3px solid var(--gray-500);
  position: relative;
  padding-right: var(--spacing-8);
}

#chatLog p.user.loading::after {
  content: "";
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 3px solid rgba(58, 12, 163, 0.3);
  border-top: 3px solid var(--secondary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

#chatLog p.user.error {
  background: rgba(220, 38, 38, 0.15);
  border-right: 3px solid rgba(220, 38, 38, 0.8);
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

.app-footer {
  text-align: center;
  margin-top: var(--spacing-8);
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

@keyframes pulseOrb {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes pulseOrbFast {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-container {
    padding: var(--spacing-3);
  }

  .app-title {
    font-size: var(--font-size-2xl);
  }

  #chatLog {
    padding: var(--spacing-3);
    max-height: 350px;
  }

  .orb {
    width: 120px;
    height: 120px;
  }
}
